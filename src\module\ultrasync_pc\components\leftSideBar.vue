<template>
    <div class="app-sidebar">
        <div class="menu-container">
            <div
                v-for="item in menuItems"
                :key="item.name"
                class="menu-item"
                :class="{ active: isMenuActive(item) }"
                @click="handleMenuClick(item)"
            >
                <div class="menu-icon">
                    <i :class="item.icon"></i>
                </div>
                <div class="menu-text">{{ item.label }}</div>
            </div>
        </div>

        <!-- 底部子菜单按钮 -->
        <div class="bottom-menu-container">
            <el-dropdown
                trigger="click"
                placement="right"
                @command="handleSubmenuCommand"
                class="bottom-dropdown"
                popper-class="custom-dropdown-right"
            >
                <div class="bottom-menu-button">
                    <div class="menu-icon">
                        <i class="el-icon-more"></i>
                    </div>
                </div>
                <el-dropdown-menu slot="dropdown" class="bottom-dropdown-menu">
                    <el-dropdown-item
                        v-for="subItem in filteredSubmenuItems"
                        :key="subItem.name"
                        :command="subItem.action"
                        :icon="subItem.icon"
                    >
                        {{ subItem.label }}
                    </el-dropdown-item>
                </el-dropdown-menu>
            </el-dropdown>
        </div>

        <!-- 系统设置弹窗 - 动态加载 -->
        <component
            :is="systemSettingComponent"
            v-model="systemSettingVisible"
            @input="handleSystemSettingVisibleChange"
        />

        <!-- 系统信息弹窗 - 动态加载 -->
        <component
            :is="versionInfoComponent"
            v-model="versionInfoVisible"
            @input="handleVersionInfoVisibleChange"
        />

        <!-- 后台管理弹窗 - 动态加载 -->
        <component
            :is="backgroundManageComponent"
            v-model="backgroundManageVisible"
            @input="handleBackgroundManageVisibleChange"
        />
    </div>
</template>
<script>
import Tool from "@/common/tool.js";
import base from "../lib/base";

export default {
    mixins: [base],
    name: 'LeftSidebar',
    data() {
        return {
            currentRoute: '',
            systemSettingVisible: false, // 系统设置弹窗是否显示
            systemSettingComponent: null, // 缓存组件
            versionInfoVisible: false, // 系统信息弹窗是否显示
            versionInfoComponent: null, // 缓存组件
            backgroundManageVisible: false, // 后台管理弹窗是否显示
            backgroundManageComponent: null, // 缓存组件
            menuItems: [
                {
                    name: 'dashboard',
                    label: '首页',
                    icon: 'el-icon-document',
                    route: '/main/dashboard'
                },
                {
                    name: 'index',
                    label: '基础平台',
                    icon: 'el-icon-notebook-2',
                    route: '/main/index/chat_window/0'
                },
                {
                    name: 'education',
                    label: '教培',
                    icon: 'el-icon-folder',
                    route: '/main/education'
                }
            ],
            submenuItems: [
                {
                    name: 'system_setting',
                    label: '设置',
                    icon: 'el-icon-setting',
                    action: 'openSystemSetting'
                },
                {
                    name: 'system_info',
                    label: '系统信息',
                    icon: 'el-icon-info',
                    action: 'openVersion'
                },
                {
                    name: 'background_manage',
                    label: '后台管理',
                    icon: 'el-icon-s-tools',
                    action: 'openBackgroundManage'
                }
            ]
        }
    },
    computed: {
        // 过滤子菜单项，根据权限显示
        filteredSubmenuItems() {

            return this.submenuItems.filter(item => {
                // 后台管理只对管理员显示
                if (item.name === 'background_manage') {
                    // 依赖响应式的初始化状态，确保权限管理器初始化后重新计算
                    console.error('isPermissionInitialized', this.$isPermissionInitialized);
                    if (!this.$isPermissionInitialized) {
                        console.warn('PermissionManager not initialized yet, hiding background_manage');
                        return false;
                    }
                    console.error('isPermissionInitialized', this.$isPermissionInitialized);
                    console.log('backgroundManage permission check:', this.$checkPermission('backgroundManage'));
                    return this.$checkPermission('backgroundManage');
                }
                // 其他菜单项都显示
                return true;
            });
        }
    },
    created() {
        this.currentRoute = this.$route.path;
    },
    mounted() {
        // 初始化当前路由

    },
    watch: {
        '$route'(to) {
            this.currentRoute = to.path;
        }
    },
    methods: {
        handleMenuClick(item) {
            if (this.currentRoute !== item.route) {
                this.$router.push(item.route);
            }
        },
        isMenuActive(item) {
            // 使用包含匹配而不是完全匹配，支持二级菜单高亮
            // 提取菜单项路由的基础路径（去掉最后的数字参数）
            const menuBasePath = item.route.replace(/\/\d+$/, '');

            // 如果当前路由以菜单项的基础路径开头，则认为是激活状态
            return this.currentRoute.startsWith(menuBasePath);
        },
        handleSubmenuCommand(command) {
            // 处理Element UI Dropdown的command事件
            switch(command) {
            case 'openSystemSetting':
                this.openSystemSetting();
                break;
            case 'openVersion':
                this.openVersion();
                break;
            case 'openBackgroundManage':
                this.openBackgroundManage();
                break;
            default:
                console.log('未知操作:', command);
            }
        },
        async openSystemSetting() {
            // 动态加载并打开系统设置弹窗
            await this.loadSystemSetting();
            this.$nextTick(() => {
                this.systemSettingVisible = true;
            });
        },

        // 动态加载SystemSetting组件
        async loadSystemSetting() {
            if (!this.systemSettingComponent) {
                try {
                    // 在用户确定打开时才动态导入组件
                    const module = await import("../pages/systemSetting.vue");
                    this.systemSettingComponent = module.default;
                } catch (error) {
                    console.error('加载SystemSetting组件失败:', error);
                    this.$message.error('加载系统设置失败，请重试');
                }
            }
        },

        // 处理弹窗显示状态变化
        handleSystemSettingVisibleChange(visible) {
            if (!visible) {
                // 弹窗关闭，延迟销毁组件以等待动画完成
                setTimeout(() => {
                    this.systemSettingComponent = null;
                }, 300); // 等待300ms动画完成
            }
        },
        async openVersion() {
            // 动态加载并打开系统信息弹窗
            await this.loadVersionInfo();
            this.$nextTick(() => {
                this.versionInfoVisible = true;
            });
        },

        // 动态加载VersionInfo组件
        async loadVersionInfo() {
            if (!this.versionInfoComponent) {
                try {
                    // 在用户确定打开时才动态导入组件
                    const module = await import("../pages/versionInfo.vue");
                    this.versionInfoComponent = module.default;
                } catch (error) {
                    console.error('加载VersionInfo组件失败:', error);
                    this.$message.error('加载系统信息失败，请重试');
                }
            }
        },

        // 处理弹窗显示状态变化
        handleVersionInfoVisibleChange(visible) {
            if (!visible) {
                // 弹窗关闭，延迟销毁组件以等待动画完成
                setTimeout(() => {
                    this.versionInfoComponent = null;
                }, 300); // 等待300ms动画完成
            }
        },
        async openBackgroundManage() {
            // 检查权限：管理员和超级管理员可以访问
            if (!this.$checkPermission('backgroundManage')) {
                this.$message.error('您没有权限访问后台管理');
                return;
            }

            // 动态加载并打开后台管理弹窗
            await this.loadBackgroundManage();
            this.$nextTick(() => {
                this.backgroundManageVisible = true;
            });
        },

        // 动态加载BackgroundManage组件
        async loadBackgroundManage() {
            if (!this.backgroundManageComponent) {
                try {
                    // 在用户确定打开时才动态导入组件
                    const module = await import("../pages/backgroundManage.vue");
                    this.backgroundManageComponent = module.default;
                } catch (error) {
                    console.error('加载BackgroundManage组件失败:', error);
                    this.$message.error('加载后台管理失败，请重试');
                }
            }
        },

        // 处理弹窗显示状态变化
        handleBackgroundManageVisibleChange(visible) {
            if (!visible) {
                // 弹窗关闭，延迟销毁组件以等待动画完成
                setTimeout(() => {
                    this.backgroundManageComponent = null;
                }, 300); // 等待300ms动画完成
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.app-sidebar {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #DDE3F0;
    border-right: none;
    position: relative;

    .menu-container {
        display: flex;
        flex-direction: column;
        padding: 8px 0;
        flex: 1;
        overflow-y: auto;

        .menu-item {
            cursor: pointer;
            text-align: center;
            width: 88px;
            height: 112px;
            margin: 2px auto;
            padding: 0;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            transition: all 0.2s ease;
            color: #171818;
            user-select: none;

            .menu-icon {
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-bottom: 8px;

                i {
                    font-size: 24px;
                    color: inherit;
                }
            }

            .menu-text {
                line-height: 22px;
                font-size: 15px;
                color: inherit;
                white-space: nowrap;
            }

            &:hover,
            &.active {
                border-radius: 6px;
                background-color: #ffffff;
                color: #009999;

                .menu-icon i {
                    color: #009999;
                }

                .menu-text {
                    color: #009999;
                }
            }
        }
    }

    // 底部子菜单容器
    .bottom-menu-container {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 1000;
        display: flex;
        justify-content: center;

        .bottom-dropdown {
            .bottom-menu-button {
                cursor: pointer;
                width: 48px;
                height: 48px;
                background-color: #ffffff;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 12px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                transition: all 0.2s ease;
                color: #171818;

                .menu-icon {
                    width: 24px;
                    height: 24px;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    i {
                        font-size: 20px;
                        color: inherit;
                        transition: transform 0.2s ease;
                    }
                }

                &:hover {
                    background-color: #009999;
                    color: #ffffff;
                    transform: translateY(-2px);
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

                    .menu-icon i {
                        color: #ffffff;
                    }
                }
            }
        }
    }
}

</style>

<style lang="scss">
/* 全局样式 - 覆盖Element UI的dropdown动画，适配右侧弹出 */
.el-dropdown-menu.el-popper {
    &.el-zoom-in-top-enter-active {
        animation: slideInFromLeft 0.18s ease-out !important;
        transform-origin: left center !important;
    }

    &.el-zoom-in-top-leave-active {
        animation: slideOutToLeft 0.18s ease-in !important;
        transform-origin: left center !important;
    }
}

@keyframes slideInFromLeft {
    0% {
        opacity: 0;
        transform: translateX(0) scale(0.9);
    }
    100% {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

@keyframes slideOutToLeft {
    0% {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateX(0) scale(0.9);
    }
}
</style>
